package Services

import (
	models "GROUPIE_TRACKER/Models"
	//"log"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"text/template"
	"time"
	"unicode"
)

// titleCase converts a string to title case without using deprecated strings.Title
func titleCase(s string) string {
	if s == "" {
		return s
	}
	runes := []rune(strings.ToLower(s))
	runes[0] = unicode.ToUpper(runes[0])
	for i := 1; i < len(runes); i++ {
		if unicode.IsSpace(runes[i-1]) {
			runes[i] = unicode.ToUpper(runes[i])
		}
	}
	return string(runes)
}

func getTemplate(w http.ResponseWriter, r *http.Request, tmpl string, data interface{}) {
	funcMap := template.FuncMap{
		"formatDate": func(dateStr string) string {
			if dateStr == "" {
				return ""
			}
			layouts := []string{
				"2006-01-02 15:04:05",
				"2006-01-02",
				"02-01-2006",
				time.RFC3339,
			}
			for _, layout := range layouts {
				t, err := time.Parse(layout, dateStr)
				if err == nil {
					if layout == "2006-01-02 15:04:05" || layout == time.RFC3339 {
						return t.Format("January 2, 2006 at 15:04")
					}
					return t.Format("January 2, 2006")
				}
			}
			return dateStr
		},
		"formatLocation": func(loc string) string {
			if loc == "" {
				return ""
			}

			// Helper function to format country codes
			formatCountryCode := func(code string) string {
				switch strings.ToLower(code) {
				case "usa":
					return "USA"
				case "uk":
					return "UK"
				case "uae":
					return "UAE"
				case "ca":
					return "Canada"
				case "au":
					return "Australia"
				case "de":
					return "Germany"
				case "fr":
					return "France"
				case "jp":
					return "Japan"
				case "br":
					return "Brazil"
				case "mx":
					return "Mexico"
				case "it":
					return "Italy"
				case "es":
					return "Spain"
				case "nl":
					return "Netherlands"
				case "be":
					return "Belgium"
				case "ch":
					return "Switzerland"
				case "at":
					return "Austria"
				case "se":
					return "Sweden"
				case "no":
					return "Norway"
				case "dk":
					return "Denmark"
				case "fi":
					return "Finland"
				case "ie":
					return "Ireland"
				case "pt":
					return "Portugal"
				case "pl":
					return "Poland"
				case "cz":
					return "Czech Republic"
				case "hu":
					return "Hungary"
				case "gr":
					return "Greece"
				case "tr":
					return "Turkey"
				case "ru":
					return "Russia"
				case "in":
					return "India"
				case "cn":
					return "China"
				case "kr":
					return "South Korea"
				case "th":
					return "Thailand"
				case "sg":
					return "Singapore"
				case "my":
					return "Malaysia"
				case "id":
					return "Indonesia"
				case "ph":
					return "Philippines"
				case "vn":
					return "Vietnam"
				case "za":
					return "South Africa"
				case "eg":
					return "Egypt"
				case "ma":
					return "Morocco"
				case "ar":
					return "Argentina"
				case "cl":
					return "Chile"
				case "co":
					return "Colombia"
				case "pe":
					return "Peru"
				case "ve":
					return "Venezuela"
				case "ec":
					return "Ecuador"
				case "uy":
					return "Uruguay"
				case "py":
					return "Paraguay"
				case "bo":
					return "Bolivia"
				case "cr":
					return "Costa Rica"
				case "pa":
					return "Panama"
				case "gt":
					return "Guatemala"
				case "hn":
					return "Honduras"
				case "sv":
					return "El Salvador"
				case "ni":
					return "Nicaragua"
				case "bz":
					return "Belize"
				case "jm":
					return "Jamaica"
				case "cu":
					return "Cuba"
				case "do":
					return "Dominican Republic"
				case "ht":
					return "Haiti"
				case "tt":
					return "Trinidad and Tobago"
				case "bb":
					return "Barbados"
				case "lc":
					return "Saint Lucia"
				case "gd":
					return "Grenada"
				case "vc":
					return "Saint Vincent and the Grenadines"
				case "ag":
					return "Antigua and Barbuda"
				case "kn":
					return "Saint Kitts and Nevis"
				case "dm":
					return "Dominica"
				case "bs":
					return "Bahamas"
				case "bm":
					return "Bermuda"
				case "ky":
					return "Cayman Islands"
				case "tc":
					return "Turks and Caicos Islands"
				case "vg":
					return "British Virgin Islands"
				case "vi":
					return "US Virgin Islands"
				case "pr":
					return "Puerto Rico"
				case "gu":
					return "Guam"
				case "as":
					return "American Samoa"
				case "mp":
					return "Northern Mariana Islands"
				case "fm":
					return "Micronesia"
				case "pw":
					return "Palau"
				case "mh":
					return "Marshall Islands"
				case "ki":
					return "Kiribati"
				case "nr":
					return "Nauru"
				case "tv":
					return "Tuvalu"
				case "to":
					return "Tonga"
				case "ws":
					return "Samoa"
				case "vu":
					return "Vanuatu"
				case "fj":
					return "Fiji"
				case "sb":
					return "Solomon Islands"
				case "pg":
					return "Papua New Guinea"
				case "nc":
					return "New Caledonia"
				case "pf":
					return "French Polynesia"
				case "ck":
					return "Cook Islands"
				case "nu":
					return "Niue"
				case "tk":
					return "Tokelau"
				case "wf":
					return "Wallis and Futuna"
				case "nz":
					return "New Zealand"
				default:
					return titleCase(code)
				}
			}

			parts := strings.Split(loc, "_")
			for i, part := range parts {
				if strings.Contains(part, "-") {
					// Split by hyphen and process each subpart
					subParts := strings.Split(part, "-")
					for j, subPart := range subParts {
						subParts[j] = formatCountryCode(subPart)
					}
					parts[i] = strings.Join(subParts, " ")
				} else {
					parts[i] = formatCountryCode(part)
				}
			}
			return strings.Join(parts, ", ")
		},
	}

	tmplPath := filepath.Join("templates", tmpl)
	templ, err := template.New(filepath.Base(tmpl)).Funcs(funcMap).ParseFiles(tmplPath)
	if err != nil {
		RenderError(w, r, http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.WriteHeader(http.StatusOK)
	if err := templ.Execute(w, data); err != nil {
		RenderError(w, r, http.StatusInternalServerError)
	}
}

func LoadHome(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		RenderError(w, r, http.StatusNotFound)
		return
	}

	artists, err := fetchArtists()
	if err != nil {
		RenderError(w, r, http.StatusInternalServerError)
		return
	}
	getTemplate(w, r, "indexTest.html", artists)
}

type ArtistPageData struct {
	models.Artists
	ActiveTab string
}

func LoadArtist(w http.ResponseWriter, r *http.Request) {
	activeTab := r.URL.Query().Get("tab")
	if activeTab == "" {
		activeTab = "overview"
	}

	if activeTab != "overview" && activeTab != "events" {
		RenderError(w, r, http.StatusNotFound)
		return
	}

	idStr := strings.TrimPrefix(r.URL.Path, "/artist/")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		RenderError(w, r, http.StatusBadRequest)
		return
	}

	artists, err := fetchArtistById(id)
	if err != nil {
		RenderError(w, r, http.StatusNotFound)
		return
	}

	if len(artists) == 0 {
		RenderError(w, r, http.StatusNotFound)
		return
	}

	artist := artists[0]
	datesLocations, err := fetchRelationData(id)
	if err != nil {
		datesLocations = make(map[string][]string)
	}

	if datesLocations == nil {
		datesLocations = make(map[string][]string)
	}

	artist.DatesLocations = datesLocations

	pageData := ArtistPageData{
		Artists:   artist,
		ActiveTab: activeTab,
	}

	getTemplate(w, r, "artist.html", pageData)
}
