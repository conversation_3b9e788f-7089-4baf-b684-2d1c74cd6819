<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{.Name}} - Artist Details</title>
  <link rel="stylesheet" href="/static/css/style2.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="artist-page">
  <div class="artist-header" style="background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url('{{.Image}}');">
    <div class="container">
      <div class="artist-info">
        <a href="/" class="back-btn">Back to Home</a>

        <h1>{{.Name}}</h1>
      </div>
    </div>
  </div>

  <div class="container">
    <!-- Tab Navigation -->
    <div class="artist-tabs">
      <a href="?tab=overview" class="tab {{if eq .ActiveTab "overview"}}active{{end}}">Overview</a>
      <a href="?tab=events" class="tab {{if eq .ActiveTab "events"}}active{{end}}">Events</a>
    </div>
    
    <!-- Tab Content -->
    <div class="tab-content-container">
      <!-- Overview Tab -->
      <div id="overview" class="tab-content" {{if ne .ActiveTab "overview"}}style="display: none;"{{end}}>
        <div class="detail-card">
          <h3><i class="fas fa-info-circle"></i> About The Artist</h3>
          <div class="artist-bio">
            <img src="{{.Image}}" alt="{{.Name}}" class="artist-bio-image">
            <div class="artist-bio-content">
              <p>Members</p>
              <div class="members-list">
                {{range .Members}}
                <span class="member-tag">{{.}}</span>
                {{end}}
              </div>
              <div class="stats-grid">
                <div class="stat-item">
                  <h4>Creation Date</h4>
                  <p>{{.CreationDate}}</p>
                </div>
                <div class="stat-item">
                  <h4>First Album</h4>
                  <p>{{.FirstAlbum}}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {{if eq .ActiveTab "events"}}
      {{if .DatesLocations}}
      <div class="detail-card">
        <h3><i class="fas fa-calendar-alt"></i> Upcoming Events</h3>
        {{range $location, $dates := .DatesLocations}}
        <div class="location-section">
          <h4><i class="fas fa-map-marker-alt"></i> {{formatLocation $location}}</h4>
          <div class="dates-list">
            {{range $date := $dates}}
            <div class="date-item"><i class="fas fa-clock"></i> {{$date}}</div>
            {{end}}
          </div>
        </div>
        {{end}}
      </div>
      
      {{end}}
    {{end}}
    
    </div>
  </div>
</body>
</html>