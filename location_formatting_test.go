package main

import (
	"fmt"
	"strings"
	"testing"
	"unicode"
)

// titleCase converts a string to title case without using deprecated strings.Title
func titleCase(s string) string {
	if s == "" {
		return s
	}
	runes := []rune(strings.ToLower(s))
	runes[0] = unicode.ToUpper(runes[0])
	for i := 1; i < len(runes); i++ {
		if unicode.IsSpace(runes[i-1]) {
			runes[i] = unicode.ToUpper(runes[i])
		}
	}
	return string(runes)
}

// formatLocation function (same as in Handler.go)
func formatLocation(loc string) string {
	if loc == "" {
		return ""
	}

	// Helper function to format country codes
	formatCountryCode := func(code string) string {
		switch strings.ToLower(code) {
		case "usa":
			return "USA"
		case "uk":
			return "UK"
		case "uae":
			return "UAE"
		case "ca":
			return "Canada"
		case "au":
			return "Australia"
		case "de":
			return "Germany"
		case "fr":
			return "France"
		case "jp":
			return "Japan"
		case "br":
			return "Brazil"
		case "mx":
			return "Mexico"
		case "it":
			return "Italy"
		case "es":
			return "Spain"
		case "nl":
			return "Netherlands"
		case "be":
			return "Belgium"
		case "ch":
			return "Switzerland"
		case "at":
			return "Austria"
		case "se":
			return "Sweden"
		case "no":
			return "Norway"
		case "dk":
			return "Denmark"
		case "fi":
			return "Finland"
		case "ie":
			return "Ireland"
		case "pt":
			return "Portugal"
		case "pl":
			return "Poland"
		case "cz":
			return "Czech Republic"
		case "hu":
			return "Hungary"
		case "gr":
			return "Greece"
		case "tr":
			return "Turkey"
		case "ru":
			return "Russia"
		case "in":
			return "India"
		case "cn":
			return "China"
		case "kr":
			return "South Korea"
		case "th":
			return "Thailand"
		case "sg":
			return "Singapore"
		case "my":
			return "Malaysia"
		case "id":
			return "Indonesia"
		case "ph":
			return "Philippines"
		case "vn":
			return "Vietnam"
		case "za":
			return "South Africa"
		case "eg":
			return "Egypt"
		case "ma":
			return "Morocco"
		case "ar":
			return "Argentina"
		case "cl":
			return "Chile"
		case "co":
			return "Colombia"
		case "pe":
			return "Peru"
		case "ve":
			return "Venezuela"
		case "ec":
			return "Ecuador"
		case "uy":
			return "Uruguay"
		case "py":
			return "Paraguay"
		case "bo":
			return "Bolivia"
		case "cr":
			return "Costa Rica"
		case "pa":
			return "Panama"
		case "gt":
			return "Guatemala"
		case "hn":
			return "Honduras"
		case "sv":
			return "El Salvador"
		case "ni":
			return "Nicaragua"
		case "bz":
			return "Belize"
		case "jm":
			return "Jamaica"
		case "cu":
			return "Cuba"
		case "do":
			return "Dominican Republic"
		case "ht":
			return "Haiti"
		case "tt":
			return "Trinidad and Tobago"
		case "bb":
			return "Barbados"
		case "lc":
			return "Saint Lucia"
		case "gd":
			return "Grenada"
		case "vc":
			return "Saint Vincent and the Grenadines"
		case "ag":
			return "Antigua and Barbuda"
		case "kn":
			return "Saint Kitts and Nevis"
		case "dm":
			return "Dominica"
		case "bs":
			return "Bahamas"
		case "bm":
			return "Bermuda"
		case "ky":
			return "Cayman Islands"
		case "tc":
			return "Turks and Caicos Islands"
		case "vg":
			return "British Virgin Islands"
		case "vi":
			return "US Virgin Islands"
		case "pr":
			return "Puerto Rico"
		case "gu":
			return "Guam"
		case "as":
			return "American Samoa"
		case "mp":
			return "Northern Mariana Islands"
		case "fm":
			return "Micronesia"
		case "pw":
			return "Palau"
		case "mh":
			return "Marshall Islands"
		case "ki":
			return "Kiribati"
		case "nr":
			return "Nauru"
		case "tv":
			return "Tuvalu"
		case "to":
			return "Tonga"
		case "ws":
			return "Samoa"
		case "vu":
			return "Vanuatu"
		case "fj":
			return "Fiji"
		case "sb":
			return "Solomon Islands"
		case "pg":
			return "Papua New Guinea"
		case "nc":
			return "New Caledonia"
		case "pf":
			return "French Polynesia"
		case "ck":
			return "Cook Islands"
		case "nu":
			return "Niue"
		case "tk":
			return "Tokelau"
		case "wf":
			return "Wallis and Futuna"
		case "nz":
			return "New Zealand"
		default:
			return titleCase(code)
		}
	}

	parts := strings.Split(loc, "_")
	for i, part := range parts {
		if strings.Contains(part, "-") {
			// Split by hyphen and process each subpart
			subParts := strings.Split(part, "-")
			for j, subPart := range subParts {
				subParts[j] = formatCountryCode(subPart)
			}
			parts[i] = strings.Join(subParts, " ")
		} else {
			parts[i] = formatCountryCode(part)
		}
	}
	return strings.Join(parts, ", ")
}

func TestLocationFormatting(t *testing.T) {
	// Test cases to demonstrate the location formatting
	testCases := []string{
		"new_york-usa",
		"los_angeles-usa",
		"london-uk",
		"paris-fr",
		"tokyo-jp",
		"sydney-au",
		"berlin-de",
		"rome-it",
		"madrid-es",
		"amsterdam-nl",
		"stockholm-se",
		"oslo-no",
		"copenhagen-dk",
		"helsinki-fi",
		"dublin-ie",
		"lisbon-pt",
		"warsaw-pl",
		"prague-cz",
		"budapest-hu",
		"athens-gr",
		"istanbul-tr",
		"moscow-ru",
		"mumbai-in",
		"beijing-cn",
		"seoul-kr",
		"bangkok-th",
		"singapore-sg",
		"kuala_lumpur-my",
		"jakarta-id",
		"manila-ph",
		"ho_chi_minh_city-vn",
		"cape_town-za",
		"cairo-eg",
		"casablanca-ma",
		"buenos_aires-ar",
		"santiago-cl",
		"bogota-co",
		"lima-pe",
		"caracas-ve",
		"quito-ec",
		"montevideo-uy",
		"asuncion-py",
		"la_paz-bo",
		"san_jose-cr",
		"panama_city-pa",
		"guatemala_city-gt",
		"tegucigalpa-hn",
		"san_salvador-sv",
		"managua-ni",
		"belize_city-bz",
		"kingston-jm",
		"havana-cu",
		"santo_domingo-do",
		"port_au_prince-ht",
		"port_of_spain-tt",
		"bridgetown-bb",
		"castries-lc",
		"st_georges-gd",
		"kingstown-vc",
		"st_johns-ag",
		"basseterre-kn",
		"roseau-dm",
		"nassau-bs",
		"hamilton-bm",
		"george_town-ky",
		"cockburn_town-tc",
		"road_town-vg",
		"charlotte_amalie-vi",
		"san_juan-pr",
		"hagatna-gu",
		"pago_pago-as",
		"saipan-mp",
		"palikir-fm",
		"ngerulmud-pw",
		"majuro-mh",
		"south_tarawa-ki",
		"yaren-nr",
		"funafuti-tv",
		"nukualofa-to",
		"apia-ws",
		"port_vila-vu",
		"suva-fj",
		"honiara-sb",
		"port_moresby-pg",
		"noumea-nc",
		"papeete-pf",
		"avarua-ck",
		"alofi-nu",
		"nukunonu-tk",
		"mata_utu-wf",
		"wellington-nz",
	}

	fmt.Println("Location Formatting Test Results:")
	fmt.Println("=================================")

	for _, testCase := range testCases {
		formatted := formatLocation(testCase)
		fmt.Printf("%-30s -> %s\n", testCase, formatted)
	}
}
